import Rails from "@rails/ujs"
Rails.start()
window.Rails = Rails
import "./jquery"
import "jquery-ui"
import "jquery-ui/ui/widgets/mouse";
import 'jquery-ui/ui/widgets/sortable'
import Sortable from "sortablejs"

import * as bootstrap from "bootstrap"
window.bootstrap = bootstrap;

import 'gridstack/dist/gridstack.min.css';
import "quill/dist/quill.snow.css"
import "dropzone/dist/dropzone.css"
import "mapbox-gl/dist/mapbox-gl.css"
import tinymce from "tinymce"
import "tinymce/skins/ui/oxide/skin.min.css"
import "slick-carousel"
import moment from "moment"
import "daterangepicker"
import "./checklist_manager"
import "daterangepicker/daterangepicker.css"
import flatpickr from "flatpickr";
import "./privacy_screen"
import "spectrum-colorpicker2"
import "spectrum-colorpicker2/dist/spectrum.min.css"
import "animate.css/animate.css"
import './admin/crm/label_modal'
import "./admin/crm/move_card_modal"
import "./admin/crm/archive_card"
import "./admin/crm/checklist_title"
import "./admin/crm/checklist_item_actions"

import toastr from "toastr";
import "toastr/build/toastr.min.css";
window.toastr = toastr;

import tippy, { hideAll } from "tippy.js";
window.tippy = tippy;
window.tippyHideAll = hideAll;
import 'tippy.js/dist/tippy.css';
import 'tippy.js/themes/light.css';

import Swal from 'sweetalert2'
window.Swal = Swal;

// Import custom modal utility
import modalUtils from './admin/crm/modal'
window.modalUtils = modalUtils;

// Import global sidebar manager
import SidebarManager from './admin/shared/sidebar_manager'
window.SidebarManager = SidebarManager;

// Import shared modal functionality

// Import device fingerprint service
// import './services/device_fingerprint_service'
// window.DeviceFingerprintService = DeviceFingerprintService;
import modalManager from './admin/shared/modals'
window.modalManager = modalManager;

// Import unified dropdown system
// import './admin/shared/global_dropdown_handler'

// Import postcode lookup functionality
import postcodeLookup from './admin/patients/postcode_lookup'
window.postcodeLookup = postcodeLookup;

// Initialize modal functionality
import './admin/shared/modal_initializer'

// Medical History Modal
import "./admin/patients/medical_histories/modal"

// Import card details functionality
import './admin/crm/card_details';

import select2 from 'select2';
select2(null, $)();

import Highcharts from "highcharts";
window.Highcharts = Highcharts;

import AirDatepicker from "air-datepicker";
window.AirDatepicker = AirDatepicker;

import { GridStack } from 'gridstack';
window.GridStack = GridStack;

// Custom JS
import "./admin/select2.js";
import "./admin/marker.js";
import "./admin/navbar/search.js";
import "./admin/navbar/tw_practice_selector.js";
import "./admin/navbar/dropdown.js";
import "./admin/navbar/tw_navbar.js";
import "./admin/general_settings/users.js";
import "./admin/general_settings/treatment_categories.js";
import "./admin/general_settings/treatments.js";
import "./admin/general_settings/calendar_reserved_block_types.js";
import "./admin/general_settings/treatment_plan_estimates_information.js";
import "./admin/general_settings/estimates_practice_info.js";
import "./admin/general_settings/estimates_shared.js";
import "./admin/general_settings/notification_templates.js";
import "./admin/general_settings/labs/labs_form.js";
import "./admin/general_settings/labs/index.js";
import "./admin/general_settings/patient_asset_labels.js";
import "./admin/general_settings/onboarding_forms.js";
import "./admin/general_settings/hr_templates.js";
import "./admin/general_settings/medical_histories.js";
import "./admin/general_settings/medical_histories_index.js";
import "./admin/general_settings/alerts.js";
import "./admin/general_settings/medications.js";

import "./admin/general_settings/patient_gps.js";
import "./admin/general_settings/cot_payment_plans.js";
import "./admin/general_settings/cot_template_notes.js";
import "./admin/general_settings/cot_categories.js";
import "./admin/general_settings/roles.js";
import "./admin/general_settings/cropper.js";
import "./admin/general_settings/shared.js";
import "./admin/general_settings/sidebar.js";
import "./admin/general_settings/dropdowns.js";
import "./admin/onboarding_forms/index.js";
import "./admin/patients/form.js";
import "./admin/patients/show.js";
import "./admin/patients/search.js";
import "./admin/patients/add_patient_handler.js";
import "./admin/patients/recently_viewed.js";
import "./admin/navbar/patient_search.js";
import "./admin/patients/patient_address.js";
import "./admin/patients/school_address.js";
import "./admin/patients/patient_note.js";
import "./admin/patients/patient_notes.js";
import "./admin/patients/pinned_notes.js";
import "./admin/patients/accessibility_alerts_modal.js";
import "./admin/patients/payment_plans_modal.js";
import "./admin/patients/invoices/form.js";
import "./admin/patients/invoices/view_payments_summary.js";
import "./admin/patients/payments/form.js";
import "./admin/patients/payments/stripe_card_form.js"
import "./admin/patients/payments/add_manual_payment.js"
import "./admin/patients/payments/link_to_payment.js"
import "./admin/patients/payments/link_to_invoice.js"
import "./admin/patients/payments/refund.js"
import "./admin/patients/payments/edit_reason.js"
import "./admin/patients/account/tabs.js";
import "./admin/patients/medical_histories/modals.js";
import "./admin/patients/medical_histories/form.js";
import "./admin/patients/medical_histories/search.js";
import "./admin/patients/medical_histories/index.js";
import "./admin/patients/recalls.js";
import "./admin/patients/letters/index.js";
import "./admin/patients/assets_filter.js";

import "./admin/general_settings/automations.js";
import "./admin/treatment_plans/index.js";
import "./admin/treatment_plan_options/new.js";
import "./admin/treatment_plan_options/information.js";
import "./admin/treatment_plan_options/oral_health.js";
import "./admin/treatment_plan_options/plan_details.js";
import "./admin/treatment_plan_options/team.js";
import "./admin/treatment_plan_options/more_information.js";
import "./admin/treatment_plan_options/forms.js";
import "./admin/treatment_plan_options/testimonials.js";
import "./admin/treatment_plan_options/shared.js";
import "./admin/shared/tinymce.js";
import "./admin/shared/ai_tools.js";
import "./admin/shared/ai_input.js";
import "./admin/shared/drop_file.js";
import "./admin/shared/datepicker.js";
import "./admin/hr_management/announcements.js";
import "./admin/hr_management/form_assignments.js"
import "./admin/hr_management/header.js"
import "./admin/hr_management/holidays.js";
import "./admin/hr_management/meetings.js";
import "./admin/hr_management/shifts.js"
import "./admin/hr_management/shifts/index.js"
import "./admin/hr_management/time_sheets.js";
import "./admin/hr_management/time_sheets_partial.js";
import "./admin/hr_management/users.js";
import "./admin/lab_works/new.js";
import "./admin/lab_works/change_status.js";
import "./admin/lab_works/patient_assets.js";
import "./admin/lab_works/show.js";
import "./admin/lab_works/index.js";
import "./admin/lab_works/table_view.js";
import "./admin/lab_work_files/upload_invoice.js";
import "./admin/lab_work_files/update_invoice.js";
import "./admin/lab_work_files/delete_invoice.js";
import "./admin/lab_work_files/invoice_modal.js";
import "./admin/lab_dockets/new.js";
import "./admin/lab_dockets/lab_docket_treatments.js";
import "./admin/lab_dockets/print_docket.js";
import "./admin/patients/charting_appointments.js";
import "./admin/conversations/chat_container.js";
import "./admin/conversations/conversation_form.js";
import "./admin/conversations/conversation_switcher.js";
import "./admin/conversations/messages_list.js";
import "./message_dropdown.js";
import "./admin/appointment_notes/calendar_sidebar_direct.js";
import "./admin/conversations/recipients_select.js";
import "./admin/conversations/whatsapp_templates.js";
import "./admin/conversations/letter_templates.js";
import "./admin/conversations/message_list.js";
import "./infinite_scroll.js";
// import "./conversation_search.js";
import "./conversation_tabs.js";
import "./admin/whatsapp_templates/template_builder.js";
import "./admin/shared/fixed_footer.js";
import "./admin/shared/ajax_submit.js";
import "./admin/general_settings/communications.js";
import "./admin/general_settings/practices.js";
import "./admin/general_settings/registered_devices.js";
import "./admin/general_settings/registered_devices_realtime.js";
import "./admin/shared/pusher.js";
import "./patients/device_selection_modal.js";
import "./admin/patients/image_uploader.js";
import "./patients/device_registration.js";
import "./patients/device_registration_waiting.js";
import "./patients/patient_layout.js";
import "./patients/patient_page_layout.js";
import "./patients/registration_form.js";
import "./tw_calendar_sidebar.js";
import "./admin/shared/debounce.js";
import "./admin/shared/actions.js";
import "./admin/shared/actions_sidebar.js";
import "./admin/shared/unified_action_modal.js";
import "./admin/shared/action_timeline_accordion.js";
import "./admin/shared/action_timeline_table.js";
import "./admin/shared/action_comments.js";
import "./admin/shared/action_complete.js";
import "./admin/shared/action_button_tooltips.js";

import "./admin/shared/tippy.js";
import "./admin/shared/truncate.js";
import "./admin/shared/dropzone.js";
import "./admin/shared/asset_upload_modal.js";
import "./shared/admin_table.js";
import "./shared/apple_filter_buttons.js";
import "./admin/notifications/notifications.js";
import "./admin/notifications/notification_toggle.js";
import "./admin/actions/actions_sidebar.js";
import "./admin/actions/reminder_modal.js";
import "./admin/patients/assets.js";
import "./admin/patients/assets_filter.js";
import "./admin/patients/image_comparison.js";
import "./admin/reports/favorite.js";
import "./admin/reports/invoices.js";
import "./admin/reports/search.js";
import "./admin/reports/overlay.js";
import "./admin/reports/takings.js";
import "./admin/reports/new_patients_per_month.js";
import "./admin/reports/patients_by_age.js";
import "./admin/reports/lapsed_patients.js";
import "./admin/reports/active_patients_per_practitioner.js";
import "./admin/reports/radiographs.js";
import "./admin/reports/prescriptions.js";
import "./admin/reports/appointments.js";
import "./admin/reports/appointments_by_month.js";
import "./admin/reports/modern_sidebar.js";
import "./admin/reports/patient_accounts.js";
import "./admin/reports/treatment_plan_revenue.js";
import "./admin/signature_requests/signature_requests.js";
import "./admin/signature_requests/index.js";
import "./admin/signature_requests/new.js";
import "./admin/crm/board.js";
import "./admin/crm/cards.js";
import "./admin/crm/summary_card.js";

import "./admin/crm/crm_labels.js";

import "./admin/crm/card_assets.js";
import "./admin/crm/card_sidebar_actions.js";
import "./admin/crm/table_view.js";
import "./admin/crm/cards/slot_finder.js";
import "./admin/prescriptions/form.js";
import "./admin/prescriptions/signature.js";
import "./admin/prescriptions/medication_dropdowns.js";
import "./admin/prescriptions/dentist_dropdown.js";
import "./admin/dashboards/dashboard.js";
import "./admin/calendar/appointment_toggle.js";
import "./admin/calendar/staff_calendar.js";
import "./admin/dashboards/update_dashboard.js";
import "./admin/dashboards/search.js";
import "./vendor/jquery.getAddress.js";

import "./admin/patients/charting/appointment_note_collapse.js";
import "./admin/patients/charting/add_delete_appointment.js";
import "./admin/patients/charting/add_course_of_treatment.js";
import "./admin/patients/charting/scroll_position_manager.js";
import "./admin/patients/charting/initialize_teeth.js";
import "./admin/patients/charting/teethgrid.js";
import "./admin/patients/charting/circle_buttons.js";
import "./admin/patients/charting/select_treatment.js";
import "./admin/patients/charting/select_appointment.js";
import "./admin/patients/charting/send_estimate.js";
import "./practices/device_registration.js";
import "./admin/patients/charting/add_treatment_to_tooth.js";
import "./admin/patients/charting/tooth_context_menu.js";
import "./admin/patients/charting/calendar_booking_modal.js";
import "./admin/patients/charting/special_keys_press.js";
import "./admin/patients/notes_dropdown.js";
import "./admin/patients/mute_notes.js";
import "./admin/patients/charting/delete_charting_treatment.js";
import "./admin/patients/charting/eraser.js";
import "./admin/patients/charting/update_totals.js";
import "./admin/patients/charting/charted_treatments.js";
import "./admin/patients/patient_notes_modal.js";
import "./admin/patients/note_color_picker.js";
import "./admin/patients/inline_editing.js";
import "./admin/patients/assigned_team_modal.js";
import "./admin/patients/assigned_practices_modal.js";
import "./admin/crm/assigned_team_modal.js";
import "./linked_family_modal.js";
import "./admin/patients/communication_consents.js";
import "./admin/patients/modern_postcode_lookup.js";
import "./admin/patients/demographics_dropdowns.js";
import "./admin/patients/editable_fields.js";
import "./admin/patients/gp_search.js";
import "./admin/patients/payment_plans_modal.js";
import "./admin/patients/note_menu.js";
import "./admin/patients/archived_notes_toggle.js";
import "./admin/patients/charting/sortable.js";
import "./admin/patients/actions.js";
import "./admin/patients/reminder_dropdown.js";
// import "./admin/patients/debug_dropdown.js";
import "./admin/patients/team_members_edit.js";
import "./admin/patients/practices_edit.js";
import "./admin/patients/temporary_patient_banner.js";
import "./admin/patients/charting/update_charting_treatment.js";
import "./admin/patients/charting/treatment_search.js";
import "./admin/patients/charting/scroll_course_of_treatments.js";
import "./admin/patients/charting/appointments_search.js";
import "./admin/patients/charting/load_base_and_history.js";
import "./admin/patients/charting/base_chart_toggle.js";
import "./admin/patients/charting/history_toggle.js";
import "./admin/patients/charting/submit_note.js";
import "./admin/patients/charting/toggle_popout.js";
import "./admin/patients/charting/payment_plan_select.js";
import "./admin/patients/charting/appointment_actions.js";
import "./admin/patients/charting/complete_cot.js"
import "./admin/patients/charting/accept_cot.js"
import "./admin/patients/charting/archive_cot.js"
import "./admin/patients/charting/apply_patient_treatment.js"
import "./admin/patients/charting/quick_charting_controls.js"
import "./admin/patients/charting/voice_charting.js"
import "./admin/patients/charting/switch_teethgrid.js"
import "./admin/patients/charting/add_cot_appointment.js"
import "./admin/patients/charting/charge_cot.js"
import "./admin/patients/charting/bpe.js"
import './admin/patients/charting/toggle_archived_cots.js'
import './admin/patients/charting/copy_cot.js'
import './admin/patients/charting/view_lab_docket.js'
import './admin/patients/charting/bh_button.js'
import './admin/patients/charting/cot_template_notes.js'
import "./admin/patients/charting/import_note_template.js";
import "./admin/patients/charting/auto_save_charted_treatment.js";
import "./admin/patients/charting/appointment_total_duration.js";
import "./admin/patients/charting/appointment_booking_time.js";
import "./admin/patients/charting/appointment_type.js";
import "./admin/patients/charting/async_modal_loader.js";
import "./admin/patients/charting/appointment_lock.js";
import "./admin/patients/charting/appointment_note_lock.js";
import "./admin/patients/charting/treatment_note_preview.js";
import "./admin/patients/charting/favorite_treatment.js";
import "./admin/patients/charting/appointment_reorder.js";
import "./admin/patients/charting/appointments_height.js";
import "./admin/patients/charting/appointment_note_delete.js";
import "./admin/patients/charting/footer_dropdown.js";
import "./admin/patients/medical_histories/modal.js";
import "./admin/patients/account/modals.js";
import "./admin/patients/account/dropdowns.js";
import "./admin/general_settings/cot_templates.js";
import "./admin/patients/charting/select_template.js";

import "./admin/patients/perio_exams/new.js";
import "./admin/patients/perio_exams/show.js";

// Conversation tabs functionality
import "./admin/patients/perio_exams/index.js";
import "./admin/patients/perio_exams/compare.js";
import "./admin/patients/perio_exams/plaque_and_bleeding.js";

import "./admin/patients/payment_plans/new.js";
import "./admin/patients/payment_plans/add_payment_method.js";

import "./patients/medical_histories/new.js";
import "./patients/online_bookings/calendar.js";
import "./patients/online_bookings/practice.js";
import "./patients/treatment_plans/accept.js";
import "./patients/two_factor";

import "./signature_requests/verify.js";
import "./signature_requests/signature_request.js";

import "./admin/event_logs/filters.js";
import "./admin/general_settings/users/user_list_items.js";
import "./admin/general_settings/users/edit.js";
import "./admin/general_settings/users/multi_select.js";
import "./admin/general_settings/users/accordion.js";

import "./admin/global/broken_image_handler"

window.toastr = toastr
