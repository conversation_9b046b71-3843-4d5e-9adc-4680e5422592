// Wrap everything in DOMContentLoaded so it only runs after the page loads
document.addEventListener('DOMContentLoaded', () => {
  // 1) Check for something unique to your user form partial
  //    so we don't run this code on every page.
  const userForm = document.querySelector("#user-form") || document.querySelector("#permissions-tab") || document.querySelector("#user_role");
  if (!userForm) return; // Bail out if the user form is not present

  // Initialize tab functionality
  initializeTabs();

  // Initialize accordion functionality
  initializeAccordions();

  // Initialize user settings filter functionality
  initializeUserSettingsFilters();

  // Initialize MFA method selection
  initializeMfaMethodSelection();

  // Initialize practice selector
  initializePracticeSelector();

  // Initialize image preview functionality
  initializeUserImagePreview();

  // Show and animate the fixed footer if it exists
  const fixedFooter = document.querySelector('.fixed-footer');
  if (fixedFooter) {
    fixedFooter.classList.add('show');
    setTimeout(() => {
      fixedFooter.classList.add('play-anim');
    }, 100);
  }

  // Edit PIN (legacy - keeping for compatibility)
  $("#edit-pin").click(function () {
    $("#user_personal_pin").show();
    $(this).hide();
  });

  // Auto-logout and Auto-privacy fields
  const privacyTimeoutField = document.getElementById("user_privacy_timeout");
  const logoutTimeoutField = document.getElementById("user_logout_timeout");
  const autoPrivacyEnabledField = document.getElementById("user_auto_privacy_enabled");
  const autoLogoutEnabledField = document.getElementById("user_auto_logout_enabled");

  if (privacyTimeoutField) {
    privacyTimeoutField.addEventListener("input", validateTimeoutValues);
  }
  if (logoutTimeoutField) {
    logoutTimeoutField.addEventListener("input", validateTimeoutValues);
  }
  if (autoPrivacyEnabledField) {
    autoPrivacyEnabledField.addEventListener("change", updatePrivacyTimeoutState);
  }
  if (autoLogoutEnabledField) {
    autoLogoutEnabledField.addEventListener("change", updateLogoutTimeoutState);
  }

  // Initialize image cropping functionality
  initializeImageCropping();

  /**
   * Initialize tab functionality
   */
  function initializeTabs() {
    const tabButtons = document.querySelectorAll('.user-tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const targetTab = button.getAttribute('data-tab');

        // Remove active class from all buttons
        tabButtons.forEach(btn => {
          btn.classList.remove('border-blue-600', 'text-blue-600');
          btn.classList.add('border-transparent', 'text-gray-500');
          btn.classList.remove('active');
        });

        // Add active class to clicked button
        button.classList.add('border-blue-600', 'text-blue-600');
        button.classList.remove('border-transparent', 'text-gray-500');
        button.classList.add('active');

        // Hide all tab panes
        tabPanes.forEach(pane => {
          pane.classList.add('hidden');
          pane.classList.remove('active');
        });

        // Show target tab pane
        const targetPane = document.getElementById(targetTab);
        if (targetPane) {
          targetPane.classList.remove('hidden');
          targetPane.classList.add('active');
        }
      });
    });
  }

  /**
   * Initialize user settings filter functionality
   */
  function initializeUserSettingsFilters() {
    const dynamicContent = document.getElementById('dynamic-content');
    const filterButtons = document.querySelectorAll('[data-group="user-settings"]');
    const accordion = document.getElementById('accordion');

    if (!dynamicContent || !filterButtons.length) return;

    // Hide the accordion initially and show dynamic content
    if (accordion) accordion.classList.add('hidden');
    dynamicContent.classList.remove('hidden');

    // Content templates for each section based on the actual accordion partials
    const sectionContent = {
      holiday: `
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-slate-800 mb-4">Holiday Settings</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label for="user_initial_holiday_allowance" class="text-sm font-medium">Initial holiday allowance</label>
              <input type="text" name="user[initial_holiday_allowance]" id="user_initial_holiday_allowance" value="${document.querySelector('[name="user[initial_holiday_allowance]"]')?.value || ''}" class="flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </div>
            <div>
              <label for="user_holiday_year_start_month" class="text-sm font-medium">Holiday year start month</label>
              <input type="text" name="user[holiday_year_start_month]" id="user_holiday_year_start_month" value="${document.querySelector('[name="user[holiday_year_start_month]"]')?.value || ''}" class="flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </div>
            <div>
              <label class="text-sm font-medium">Current holiday allowance</label>
              <p class="text-sm font-medium text-slate-800 mt-2">${window.userHolidayAllowance || 0}</p>
            </div>
          </div>
        </div>
      `,
      breaks: `
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-slate-800 mb-4">Break Settings</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label for="user_break_duration" class="text-sm font-medium">Break duration (minutes)</label>
              <input type="number" name="user[break_duration]" id="user_break_duration" value="${document.querySelector('[name="user[break_duration]"]')?.value || ''}" class="flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </div>
            <div>
              <label for="user_break_interval" class="text-sm font-medium">Break interval (hours)</label>
              <input type="number" name="user[break_interval]" id="user_break_interval" value="${document.querySelector('[name="user[break_interval]"]')?.value || ''}" class="flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </div>
            <div class="flex items-center">
              <div class="form-check d-flex justify-content-between w-100 ps-0">
                <label for="user_breaks_paid" class="text-sm font-medium">Breaks paid</label>
                <input type="checkbox" name="user[breaks_paid]" id="user_breaks_paid" ${document.querySelector('[name="user[breaks_paid]"]')?.checked ? 'checked' : ''} class="form-check-input green-checkbox" />
              </div>
            </div>
          </div>
        </div>
      `,
      diagnocat: `
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-slate-800 mb-4">Diagnocat Settings</h3>
          <div class="flex gap-6">
            <div class="flex-1">
              <label for="user_diagnocat_email" class="text-sm font-medium">Diagnocat email</label>
              <input type="text" name="user[diagnocat_email]" id="user_diagnocat_email" value="${document.querySelector('[name="user[diagnocat_email]"]')?.value || ''}" class="flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </div>
            <div class="flex-1">
              <label for="user_diagnocat_password" class="text-sm font-medium">Diagnocat password</label>
              <input type="password" name="user[diagnocat_password]" id="user_diagnocat_password" value="${document.querySelector('[name="user[diagnocat_password]"]')?.value || ''}" class="flex h-10 w-full rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" />
            </div>
          </div>
        </div>
      `,
      login: `
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-slate-800 mb-4">Login Restrictions</h3>
          <div class="flex flex-row gap-6">
            <div class="grid grid-cols-[150px_1fr_160px] gap-4 flex-grow">
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Sunday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Monday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Tuesday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Wednesday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Thursday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Friday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
              <div class="contents py-2">
                <div class="flex items-center">
                  <p class="text-sm font-medium text-slate-700">Saturday</p>
                </div>
                <div class="flex items-center gap-2">
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                  <span>-</span>
                  <input type="time" class="form-control w-28 text-xs border border-[#e4e4e7] rounded-md px-3 py-2" />
                </div>
                <div class="flex items-center gap-2">
                  <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                  <label class="text-sm text-slate-600">Login Anytime</label>
                </div>
              </div>
            </div>
            <div class="col">
              <h4 class="font-semibold text-sm text-slate-800 mb-4">Timeout Settings</h4>
              <div class="flex flex-col gap-4">
                <div class="flex flex-col gap-2">
                  <div class="flex items-center gap-2">
                    <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                    <label class="text-sm font-medium">Enable Auto Privacy Screen</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <input type="number" class="flex h-10 rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm w-20" />
                    <span class="text-sm text-slate-600">minutes</span>
                  </div>
                </div>
                <div class="flex flex-col gap-2">
                  <div class="flex items-center gap-2">
                    <input type="checkbox" class="h-4 w-4 border border-[#18181b] rounded-sm" />
                    <label class="text-sm font-medium">Enable Auto Logout</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <input type="number" class="flex h-10 rounded-md border border-[#e4e4e7] bg-background px-3 py-2 text-sm w-20" />
                    <span class="text-sm text-slate-600">minutes</span>
                  </div>
                </div>
                <p class="text-xs text-slate-500">Auto privacy activates after inactivity. Auto logout must be longer than privacy timeout.</p>
              </div>
            </div>
          </div>
        </div>
      `,
      ip: `
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-slate-800 mb-4">IP Restrictions</h3>
          <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between">
              <p class="text-sm text-slate-600">Manage allowed IP addresses for this user</p>
              <button type="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors h-10 px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white">
                Add IP
              </button>
            </div>
            <div class="bg-white rounded-lg border border-slate-200 p-4">
              <div class="text-center text-slate-500 py-8">
                <p class="text-sm">No IP restrictions configured</p>
                <p class="text-xs mt-1">Click "Add IP" to restrict login access to specific IP addresses</p>
              </div>
            </div>
          </div>
        </div>
      `,
      pay: `
        <div class="bg-gray-50 rounded-lg p-6">
          <h3 class="text-lg font-semibold text-slate-800 mb-4">Associate Pay</h3>
          <p class="text-gray-600">Associate pay settings will be displayed here.</p>
        </div>
      `
    };

    // Show holiday by default
    dynamicContent.innerHTML = sectionContent.holiday;

    // Handle filter button clicks
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        const section = button.dataset.section;

        if (section && sectionContent[section]) {
          // Load the content
          dynamicContent.innerHTML = sectionContent[section];

          // Ensure dynamic content is visible and accordion is hidden
          dynamicContent.classList.remove('hidden');
          if (accordion) accordion.classList.add('hidden');

          // Scroll to the content
          dynamicContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      });
    });
  }

  /**
   * Initialize accordion functionality for collapsible sections
   */
  function initializeAccordions() {
    // Holiday accordion
    const holidayToggle = document.getElementById('holiday-toggle');
    const holidayContent = document.getElementById('holiday-content');

    if (holidayToggle && holidayContent) {
      holidayToggle.addEventListener('click', () => {
        const icon = holidayToggle.querySelector('.material-symbols-outlined');
        if (holidayContent.classList.contains('hidden')) {
          holidayContent.classList.remove('hidden');
          icon.style.transform = 'rotate(180deg)';
        } else {
          holidayContent.classList.add('hidden');
          icon.style.transform = 'rotate(0deg)';
        }
      });
    }

    // Login restrictions accordion
    const loginRestrictionsToggle = document.getElementById('login-restrictions-toggle');
    const loginRestrictionsContent = document.getElementById('login-restrictions-content');

    if (loginRestrictionsToggle && loginRestrictionsContent) {
      loginRestrictionsToggle.addEventListener('click', () => {
        const icon = loginRestrictionsToggle.querySelector('.material-symbols-outlined');
        if (loginRestrictionsContent.classList.contains('hidden')) {
          loginRestrictionsContent.classList.remove('hidden');
          icon.style.transform = 'rotate(180deg)';
        } else {
          loginRestrictionsContent.classList.add('hidden');
          icon.style.transform = 'rotate(0deg)';
        }
      });
    }
  }



  /**
   * Initialize image cropping functionality
   */
  function initializeImageCropping() {
    const imageUpload = document.getElementById('image-upload');
    const cropModal = document.getElementById('cropModal');
    const cropImage = document.getElementById('cropImage');
    const cropButton = document.getElementById('cropButton');
    const imageCanvas = document.getElementById('imageCanvas');

    if (imageUpload) {
      imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = function(event) {
            cropImage.src = event.target.result;
            cropModal.classList.remove('hidden');
            // Initialize cropping library here if needed
          };
          reader.readAsDataURL(file);
        }
      });
    }

    if (cropButton) {
      cropButton.addEventListener('click', function() {
        // Implement cropping logic here
        cropModal.classList.add('hidden');
      });
    }
  }

  // Run validation and state updates on page load
  validateTimeoutValues();
  updatePrivacyTimeoutState();
  updateLogoutTimeoutState();

  /**
   * Validate timeout values
   */
  function validateTimeoutValues() {
    const privacyField = document.getElementById("user_privacy_timeout");
    const logoutField = document.getElementById("user_logout_timeout");

    if (privacyField && logoutField) {
      const privacyValue = parseInt(privacyField.value);
      const logoutValue = parseInt(logoutField.value);

      if (privacyValue && logoutValue && privacyValue >= logoutValue) {
        logoutField.setCustomValidity("Logout timeout must be greater than privacy timeout");
      } else {
        logoutField.setCustomValidity("");
      }
    }
  }

  /**
   * Update privacy timeout state
   */
  function updatePrivacyTimeoutState() {
    const enabledField = document.getElementById("user_auto_privacy_enabled");
    const timeoutField = document.getElementById("user_privacy_timeout");

    if (enabledField && timeoutField) {
      timeoutField.disabled = enabledField.value !== "true";
    }
  }

  /**
   * Update logout timeout state
   */
  function updateLogoutTimeoutState() {
    const enabledField = document.getElementById("user_auto_logout_enabled");
    const timeoutField = document.getElementById("user_logout_timeout");

    if (enabledField && timeoutField) {
      timeoutField.disabled = enabledField.value !== "true";
    }
  }

  // Dropdown toggling
  function toggleDropdown(event) {
    event.preventDefault();
    const button = event.currentTarget;
    const dropdown = button.nextElementSibling;

    if (dropdown) {
      const isVisible = !dropdown.classList.contains("hidden");

      // Close all dropdowns
      document.querySelectorAll(".dropdown-menu").forEach(menu => {
        menu.classList.add("hidden");
      });

      // Toggle current dropdown
      if (!isVisible) {
        dropdown.classList.remove("hidden");
      }
    }
  }

  // Hide open dropdowns if clicking outside
  document.addEventListener("click", function (event) {
    const allDropdowns = document.querySelectorAll("#communications-tab .dropdown-menu");
    allDropdowns.forEach(function (dropdown) {
      if (!dropdown.contains(event.target) && !dropdown.previousElementSibling.contains(event.target)) {
        dropdown.classList.add("hidden");
      }
    });
  });

  // Remove account
  function removeAccount(accountId, event) {
    event.preventDefault();

    if (confirm("Are you sure you want to remove this account?")) {
      const accountElement = document.querySelector(`[data-account-id="${accountId}"]`);
      if (accountElement) {
        accountElement.remove();
        // Here you would typically make an AJAX call to remove from backend
      }
    }
  }

  setTimeout(() => {
    $('.tab-content').removeClass('hidden');
  }, 200);

  /**
   * Initialize MFA method selection functionality
   */
  function initializeMfaMethodSelection() {
    const mfaButtons = document.querySelectorAll('.mfa-method-btn');
    const mfaHiddenField = document.querySelector('[name="user[mfa_method]"]');
    const mfaContents = document.querySelectorAll('.mfa-content');
    const mobilePhoneField = document.querySelector('[name="user[mobile_phone]"]');
    const smsWarning = document.getElementById('sms-warning');

    console.log('MFA Debug:', {
      buttons: mfaButtons.length,
      hiddenField: !!mfaHiddenField,
      contents: mfaContents.length,
      hiddenFieldValue: mfaHiddenField?.value
    });

    if (!mfaButtons.length || !mfaHiddenField) return;

    // Function to check mobile phone and update SMS button state
    function updateSmsButtonState() {
      const smsButton = document.querySelector('[data-method="sms"]');
      const mobilePhoneValue = mobilePhoneField ? mobilePhoneField.value.trim() : '';

      if (smsButton) {
        if (!mobilePhoneValue) {
          // Disable SMS button and show warning
          smsButton.disabled = true;
          smsButton.classList.add('opacity-50', 'cursor-not-allowed');
          smsButton.classList.remove('hover:bg-blue-200');
          if (smsWarning) smsWarning.classList.remove('hidden');

          // If SMS is currently selected, switch to password
          if (mfaHiddenField.value === 'sms') {
            const passwordButton = document.querySelector('[data-method="password"]');
            if (passwordButton) {
              passwordButton.click();
            }
          }
        } else {
          // Enable SMS button and hide warning
          smsButton.disabled = false;
          smsButton.classList.remove('opacity-50', 'cursor-not-allowed');
          smsButton.classList.add('hover:bg-blue-200');
          if (smsWarning) smsWarning.classList.add('hidden');
        }
      }
    }

    // Listen for mobile phone field changes
    if (mobilePhoneField) {
      mobilePhoneField.addEventListener('input', updateSmsButtonState);
      mobilePhoneField.addEventListener('blur', updateSmsButtonState);
    }

    // Initial check
    updateSmsButtonState();

    // Function to show specific MFA content
    function showMfaContent(method) {
      const methodLower = method.toLowerCase();
      console.log('showMfaContent called with method:', method, 'lowercase:', methodLower);

      // Hide all content sections except the target
      mfaContents.forEach(content => {
        const contentId = content.id;
        if (contentId !== `${methodLower}-content`) {
          console.log('Hiding content:', contentId);
          content.classList.add('hidden');
        }
      });

      // Show the selected content
      const targetContent = document.getElementById(`${methodLower}-content`);
      console.log('Target content found:', !!targetContent, targetContent?.id);
      if (targetContent) {
        targetContent.classList.remove('hidden');
        console.log('Showing content:', targetContent.id);
      }
    }

    // Handle MFA method button clicks
    mfaButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Don't proceed if button is disabled
        if (button.disabled) {
          return;
        }

        const method = button.dataset.method;

        // Reset all buttons
        mfaButtons.forEach(btn => {
          btn.classList.remove('bg-blue-100', 'text-blue-600', 'border-blue-200', 'bg-orange-100', 'text-orange-600', 'border-orange-200', 'bg-teal-100', 'text-teal-600', 'border-teal-200');
          btn.classList.add('bg-white', 'text-gray-600', 'border-gray-200');
        });

        // Set active button styling based on method
        if (method === 'sms') {
          button.classList.remove('bg-white', 'text-gray-600', 'border-gray-200');
          button.classList.add('bg-blue-100', 'text-blue-600', 'border-blue-200');
        } else if (method === 'pin') {
          button.classList.remove('bg-white', 'text-gray-600', 'border-gray-200');
          button.classList.add('bg-teal-100', 'text-teal-600', 'border-teal-200');
        } else if (method === 'password') {
          button.classList.remove('bg-white', 'text-gray-600', 'border-gray-200');
          button.classList.add('bg-orange-100', 'text-orange-600', 'border-orange-200');
        }

        // Show the corresponding content
        showMfaContent(method);

        // Update hidden field
        mfaHiddenField.value = method;
      });
    });

    // Initialize with the appropriate default method
    let defaultMethod = mfaHiddenField.value || 'sms';

    // If SMS is selected but no mobile phone, default to password
    const mobilePhoneValue = mobilePhoneField ? mobilePhoneField.value.trim() : '';
    if (defaultMethod === 'sms' && !mobilePhoneValue) {
      defaultMethod = 'password';
      mfaHiddenField.value = 'password';
    }

    console.log('Initializing MFA with method:', defaultMethod);
    showMfaContent(defaultMethod);

    // Set the default button styling
    const defaultButton = document.querySelector(`[data-method="${defaultMethod}"]`);
    if (defaultButton) {
      // Reset all buttons first
      mfaButtons.forEach(btn => {
        btn.classList.remove('bg-blue-100', 'text-blue-600', 'border-blue-200', 'bg-orange-100', 'text-orange-600', 'border-orange-200', 'bg-teal-100', 'text-teal-600', 'border-teal-200');
        btn.classList.add('bg-white', 'text-gray-600', 'border-gray-200');
      });

      // Set active button styling based on method
      if (defaultMethod === 'sms') {
        defaultButton.classList.remove('bg-white', 'text-gray-600', 'border-gray-200');
        defaultButton.classList.add('bg-blue-100', 'text-blue-600', 'border-blue-200');
      } else if (defaultMethod === 'pin') {
        defaultButton.classList.remove('bg-white', 'text-gray-600', 'border-gray-200');
        defaultButton.classList.add('bg-teal-100', 'text-teal-600', 'border-teal-200');
      } else if (defaultMethod === 'password') {
        defaultButton.classList.remove('bg-white', 'text-gray-600', 'border-gray-200');
        defaultButton.classList.add('bg-orange-100', 'text-orange-600', 'border-orange-200');
      }
    }
  }

  /**
   * Initialize user image preview functionality
   */
  function initializeUserImagePreview() {
    const input = document.getElementById("profileImage");
    const preview = document.getElementById("previewImage");
    const placeholder = document.getElementById("upload-placeholder");

    if (!input || !preview || !placeholder) return;

    input.addEventListener("change", (event) => {
      const file = event.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = e => {
          preview.src = e.target.result;
          preview.classList.remove("hidden");
          placeholder.style.display = "none";
        };
        reader.readAsDataURL(file);
      }
    });
  }

  /**
   * Initialize practice selector functionality
   */
  function initializePracticeSelector() {
    // Only run if practice selector exists
    const selectorBtn = document.getElementById('practice-selector-btn');
    if (!selectorBtn) return;

    const modal = document.getElementById('user-practice-modal');
    const closeBtn = document.getElementById('close-user-practice-modal');
    const saveBtn = document.getElementById('save-user-practices');
    const searchInput = document.getElementById('user-practices-search-input');
    const practicesList = document.getElementById('user-practices-list');
    const selectedDisplay = document.getElementById('selected-practices-display');

    // Open modal
    selectorBtn.addEventListener('click', (e) => {
      e.preventDefault(); // Prevent form submission
      modal.classList.remove('hidden');
      searchInput.focus();
    });

    // Close modal
    function closeModal() {
      modal.classList.add('hidden');
      searchInput.value = '';
      filterPractices('');
    }

    closeBtn.addEventListener('click', (e) => {
      e.preventDefault();
      closeModal();
    });

    // Close modal when clicking backdrop
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    // Search functionality
    function filterPractices(query) {
      const practices = practicesList.querySelectorAll('[data-practice-id]');
      practices.forEach(practice => {
        const searchText = practice.dataset.practiceSearch;
        const matches = searchText.includes(query.toLowerCase());
        practice.style.display = matches ? 'flex' : 'none';
      });
    }

    searchInput.addEventListener('input', (e) => {
      filterPractices(e.target.value.trim());
    });

    // Handle checkbox changes
    practicesList.addEventListener('change', (e) => {
      if (e.target.classList.contains('user-practice-checkbox')) {
        const practiceItem = e.target.closest('[data-practice-id]');
        practiceItem.dataset.selected = e.target.checked ? 'true' : 'false';
      }
    });

    // Handle clicking on practice items (not just checkboxes)
    practicesList.addEventListener('click', (e) => {
      // Don't trigger if clicking on checkbox
      if (e.target.type === 'checkbox') return;

      const practiceItem = e.target.closest('[data-practice-id]');
      if (!practiceItem) return;

      const checkbox = practiceItem.querySelector('.user-practice-checkbox');
      if (checkbox) {
        checkbox.checked = !checkbox.checked;
        practiceItem.dataset.selected = checkbox.checked ? 'true' : 'false';
      }
    });

    // Save selection
    saveBtn.addEventListener('click', (e) => {
      e.preventDefault(); // Prevent form submission

      const selectedPractices = [];
      const checkboxes = practicesList.querySelectorAll('.user-practice-checkbox:checked');

      checkboxes.forEach(checkbox => {
        const practiceId = checkbox.value;
        const practiceItem = checkbox.closest('[data-practice-id]');
        const practiceName = practiceItem.dataset.practiceName;
        selectedPractices.push({
          id: practiceId,
          name: practiceName
        });
      });

      updateSelectedDisplay(selectedPractices);
      closeModal();
    });

    // Update the selected practices display
    function updateSelectedDisplay(practices) {
      // Clear existing hidden inputs
      const existingInputs = document.querySelectorAll('input[name="user[practice_ids][]"]');
      existingInputs.forEach(input => input.remove());

      if (practices.length === 0) {
        selectedDisplay.classList.add('hidden');
        return;
      }

      selectedDisplay.classList.remove('hidden');
      const container = selectedDisplay.querySelector('.flex.flex-wrap');

      // Clear existing display
      container.innerHTML = '';

      // Add each selected practice
      practices.forEach(practice => {
        const practiceElement = createPracticeElement(practice);
        container.appendChild(practiceElement);
      });
    }

    // Create practice element for display
    function createPracticeElement(practice) {
      const div = document.createElement('div');
      div.className = 'flex items-center gap-2 p-1.5 pr-2 bg-blue-50 border border-blue-200 rounded-md text-sm';
      div.dataset.practiceId = practice.id;

      div.innerHTML = `
        <span class="relative flex shrink-0 overflow-hidden rounded-full h-5 w-5">
          <div class="bg-blue-100 h-full w-full flex items-center justify-center text-blue-600 font-medium text-xs">
            ${practice.name.charAt(0).toUpperCase()}
          </div>
        </span>
        <span class="font-medium text-blue-900">${practice.name}</span>
        <button type="button" class="remove-practice-btn text-blue-400 hover:text-blue-600 transition-colors" data-practice-id="${practice.id}">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
        <input type="hidden" name="user[practice_ids][]" value="${practice.id}" />
      `;

      return div;
    }

    // Handle remove practice buttons
    selectedDisplay.addEventListener('click', (e) => {
      if (e.target.closest('.remove-practice-btn')) {
        e.preventDefault(); // Prevent form submission
        e.stopPropagation(); // Stop event bubbling

        const practiceId = e.target.closest('.remove-practice-btn').dataset.practiceId;
        const practiceElement = e.target.closest('[data-practice-id]');

        // Remove from display
        practiceElement.remove();

        // Uncheck in modal
        const checkbox = practicesList.querySelector(`#user-practice-checkbox-${practiceId}`);
        if (checkbox) {
          checkbox.checked = false;
          checkbox.closest('[data-practice-id]').dataset.selected = 'false';
        }

        // Hide display if no practices left
        const remainingPractices = selectedDisplay.querySelectorAll('[data-practice-id]');
        if (remainingPractices.length === 0) {
          selectedDisplay.classList.add('hidden');
        }
      }
    });
  }

  // Export functions for global access
  window.toggleDropdown = toggleDropdown;
  window.removeAccount = removeAccount;
});
