# frozen_string_literal: true

module Admin
  module GeneralSettings
    class UsersController < Admin::ApplicationController
      before_action :set_user, only: %i[edit update archive update_treatment_plans_view]

      def index
        @query = policy_scope(User).includes(:roles).ransack(params[:query])
        @users = @query.result(distinct: true).paginate(page: params[:page], per_page: 20)

        respond_to do |format|
          format.html
          format.json do
            users = policy_scope(User).where(archived: false).order(:first_name, :last_name)
            render json: users.map { |user|
              {
                id: user.id,
                first_name: user.first_name,
                last_name: user.last_name,
                email: user.email,
                image_url: user.image.attached? ? url_for(user.image) : nil
              }
            }
          end
        end
      end

      def new
        @user = User.new
      end

      def edit
        @practices = Practice.all
      end

      def create
        @user = User.new(user_params)
        @user.skip_password_validation = true
        if @user.save
          reset_token = @user.send_reset_password_instructions
          UserMailer.welcome_and_setup_password(@user, reset_token).deliver_now
          flash[:success] = 'User created and welcome email sent'
          redirect_to edit_admin_general_settings_user_path(@user)
        else
          flash[:error] = ['Error creating user', @user.errors.first&.full_message]
          render :new
        end
      end

      def update
        @user.skip_password_validation = true if params[:user][:password].blank?
        if @user.update(user_params)
          flash[:success] = 'User updated'
          redirect_to edit_admin_general_settings_user_path(@user)
        else
          flash[:error] = ['Error updating user', @user.errors.first&.full_message]
          render :edit
        end
      end

      def archive
        user = User.find(params[:id])
        user.update!(archived: true)
        flash[:success] = 'User archived'
        redirect_back(fallback_location: admin_general_settings_users_path)
      end

      def unarchive
        user = User.find(params[:id])
        user.update!(archived: false)
        flash[:success] = 'User unarchived'
        redirect_back(fallback_location: admin_general_settings_users_path)
      end

      def destroy
        user = User.find(params[:id])
        user.destroy!
        flash[:success] = 'User deleted'
        redirect_back(fallback_location: admin_general_settings_users_path)
      end

      def update_treatment_plans_view
        if @user.update(show_treatment_plans: params[:value])
          render json: { success: true }
        else
          render json: { success: false, error: user.errors.full_messages }, status: :unprocessable_entity
        end
      end

      def search
        query = params[:q].to_s.strip

        # Set up base query for this practice
        users_scope = User.all

        if query.present?
          if query.include?(' ')
            # If query contains spaces, try to match as full name
            name_parts = query.split(' ', 2)
            first_part = name_parts[0]
            last_part = name_parts[1]

            # Use Ransack for more efficient searching
            @q = users_scope.ransack(
              m: 'or',
              first_name_cont: first_part,
              last_name_cont: last_part,
              email_cont: query
            )
          else
            # Single word query - search across fields
            @q = users_scope.ransack(
              m: 'or',
              first_name_cont: query,
              last_name_cont: query,
              email_cont: query
            )
          end

          @users = @q.result(distinct: true).limit(10)
        else
          # No query, just return first 10 users
          @users = users_scope.limit(10)
        end

        render json: @users.map { |user|
          {
            id: user.id,
            name: [user.first_name, user.last_name].join(' ').strip,
            email: user.email
          }
        }
      end

      def select2_search
        users_scope = policy_scope(User).where(archived: false)

        # Filter by role if specified
        users_scope = users_scope.joins(:roles).where(roles: { name: params[:role] }) if params[:role].present?

        results = users_scope
                  .ransack(fuzzy_search: params[:q])
                  .result(distinct: true)
                  .limit(20)
                  .map do |user|
                    {
                      id: user.id,
                      text: user.full_name_with_email,
                      image_url: if user.image.attached?
                                   url_for(user.image)
                                 else
                                   ActionController::Base.helpers.asset_path('default-avatar.webp')
                                 end
                    }
                  end

        render json: { results: }, status: :ok
      end

      private

      def set_user
        @user = User.find(params[:id])
      end

      def user_params
        params.require(:user).permit(
          :address_line_1,
          :address_line_2,
          :online_booking_description,
          :title,
          :first_name,
          :last_name,
          :email,
          :mobile_phone,
          :password,
          :password_confirmation,
          :mfa_method,
          :pin,
          :pin_confirmation,
          :image,
          :initial_holiday_allowance,
          :holiday_year_start_month,
          :accrue_holidays,
          :breaks,
          :break_duration,
          :break_interval,
          :breaks_paid,
          :diagnocat_email,
          :diagnocat_password,
          :override_pin,
          :auto_privacy_enabled,
          :auto_logout_enabled,
          :privacy_timeout,
          :logout_timeout,
          :lab_user,
          :gdc_number,
          :show_treatment_plans,
          role_ids: [],
          practice_ids: []
        )
      end
    end
  end
end
